<%@ page contentType="text/html; charset=GB2312" %>
<%@ page import="java.util.List" %>
<%@ page import="com.wms.hibernate.ocr.OCRProductTraycodeInfoHis" %>
<%@ page import="java.text.DecimalFormat" %>
<%
    String condition = (String) request.getAttribute("condition");
%>
<html>
<head>
    <title>自动化立体仓库信息管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache">
    <meta http-equiv="expires" content="0">
    <meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
    <meta http-equiv="description" content="This is my page">
    <link href="<%=request.getContextPath()%>/js/Dialog18/style/default.css" rel="stylesheet" type="text/css">
    <link href="<%=request.getContextPath()%>/css/table.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="<%=request.getContextPath()%>/js/lodop/LodopFuncs.js"></script>
    <object id="LODOP_OB" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" width=0 height=0>
        <embed id="LODOP_EM" type="application/x-print-lodop" width=0 height=0></embed>
    </object>
    <style media=print>
        <!--
        .Noprint {
            display: none;
        }

        /*用本样式在打印时隐藏非打印项目*/
        .PageNext {
            page-break-after: always;
        }

        /*控制分页*/
        -->
    </style>
    <style type="text/css">
        thead {
            color: green;
            font-weight: bold;
            font-size: 14px;
        }

        tbody {
            color: blue;
            font-size: 12px;
        }

        tfoot {
            color: red;
        }
    </style>
    <script language="JavaScript">
        var ac = "<%=request.getContextPath()%>/service?actionCode=";
        var id = "<%=(String)request.getAttribute("id")%>";
        var HKEY_Root, HKEY_Path, HKEY_Key;
        HKEY_Root = "HKEY_CURRENT_USER";
        HKEY_Path = "\\Software\\Microsoft\\Internet Explorer\\PageSetup\\";

        //设置网页打印的页眉页脚
        function PageSetup_Null() {
            try {
                var Wsh = new ActiveXObject("WScript.Shell");
                HKEY_Key = "header";
                Wsh.RegWrite(HKEY_Root + HKEY_Path + HKEY_Key, "");
                HKEY_Key = "footer";
                Wsh.RegWrite(HKEY_Root + HKEY_Path + HKEY_Key, "&b第&p/&P页");
            } catch (e) {
            }
        }

        function DownFile() {
            window.location.href = ac + "DownFileAction&report_type=ReportOCRAbnormal&file_type=excel&file_name=ocrAbnormal.xls&condition=<%=condition%>";
        }

        /*打印*/
        function Print() {
            PageSetup_Null();
            window.print();
        }

        function CreateTwoFormPage() {
            var LODOP; //声明为全局变量
            LODOP = getLodop();
            LODOP.PRINT_INIT("OCR异常统计");
            LODOP.SET_PRINT_PAGESIZE(2, "210mm", "297mm", "BANCITJ");
            var strStyle = "<style> table,td,th {border-width: 0px;border-style: solid;}</style>";
            LODOP.ADD_PRINT_TABLE("5mm", "3mm", "280mm", "198mm", strStyle + document.getElementById("div1").innerHTML);
            LODOP.SET_SHOW_MODE("LANDSCAPE_DEFROTATED", 1);
            LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREWINDOW", 1);//打印后自动关闭预览窗口border-collapse: collapse
            LODOP.PREVIEW();
        };
    </script>
</head>

<body>
<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="Noprint">
    <tr>
        <td height="27">
            <OBJECT id=WebBrowser classid=CLSID:8856F961-340A-11D0-A96B-00C04FD705A2 height=0 width=0></OBJECT>
            <input onclick="DownFile()" type="button" name="button5" value="导出EXCEL" class="BUTTON_STYLE1">
            <input onclick="CreateTwoFormPage()" type="button" name="button5" value="打印" class="BUTTON_STYLE1">
            <input onclick="window.close()" type="button" name="button8" value="关闭" class="BUTTON_STYLE1">
        </td>
    </tr>
</table>

<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" class="Noprint">
    <tr>
        <td height="10">
            <hr align="center" color="#006699" noshade="noshade" size="1">
        </td>
    </tr>
</table>

<!-- 打印部分 -->
<div id="div1">
    <table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="MAIN_REPORT">
        <thead>
        <!-- 表头(start) -->
        <tr>
            <td colspan="10">
                <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" bordercolor="#FFFFFF">
                    <tr>
                        <td height="50" valign="top" align="center">
                            <div width="98%" align="center" style="display: table-header-group; font-size: 24px;">&nbsp;&nbsp;OCR异常数据报表
                                <hr align="center" color="#006699" noshade="noshade" size="1" width="1800">
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr style="height: 30px; ">
            <td style="border:1px solid #000000" class="TD_LIST_TITLE" align="center" width="50">行号</td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center" width="550">箱码
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center">产品编码
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center">产品名称
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center">批次
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center">产地编码
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center">单位
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center">添加时间
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center">线体
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center">异常类型
            </td>
        </tr>
        </thead>
        <tbody>
        <!-- 表头(end) -->
        <!-- 表体(start) -->
        <%
            List ls = (List) request.getAttribute("exResultList");
            OCRProductTraycodeInfoHis data = null;
            String s0, s1, s2, s3, s4, s5, s6, s7, s8, s9 = null;
            String product_id = "", data_id = "";
            int ngCount = 0;
            int bmCount = 0;
            int totalCount = 0;
            
            if (ls != null && ls.size() > 0) {
                totalCount = ls.size();
                for (int i = 0; i < ls.size(); i++) {
                    data = (OCRProductTraycodeInfoHis) ls.get(i);
                    data_id = data.getId();
                    s0 = data.getBoxno();
                    product_id = data.getProductid();
                    if (product_id.contains("BM")) {
                        s1 = data.getProductid();
                        s2 = "白码";
                        s9 = "白码异常";
                        bmCount++;
                    } else {
                        s1 = data.getProductid();
                        s2 = "NG码";
                        s9 = "NG码异常";
                        ngCount++;
                    }
                    s3 = data.getBatchno();
                    s4 = data.getZbatchno();
                    s5 = data.getUnit();
                    s6 = data.getReserve2();
                    s7 = data.getReserve5();
                    if (s7.equals("192.168.11.11")) {
                        s8 = "A线";
                    } else if (s7.equals("192.168.11.12")) {
                        s8 = "B线";
                    } else {
                        s8 = "未知";
                    }
        %>
        <tr onmouseover="this.bgColor='#E2E8EA'" onmouseout="this.bgColor=''" style="height: 30px; ">
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-left: 1px solid #000000"
                class="TD_LIST" align="center"><%=i + 1%>
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" class="TD_LIST"
                align="center"><%=s0 == null ? "" : s0%>
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" class="TD_LIST"
                align="center"><%=s1 == null ? "" : s1%>
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" class="TD_LIST"
                align="center"><%=s2 == null ? "" : s2%>
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" class="TD_LIST"
                align="center"><%=s3 == null ? "" : s3%>
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" class="TD_LIST"
                align="center"><%=s4 == null ? "" : s4%>
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" class="TD_LIST"
                align="center"><%=s5 == null ? "" : s5%>
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" class="TD_LIST"
                align="center"><%=s6 == null ? "" : s6%>
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" class="TD_LIST"
                align="center"><%=s8 == null ? "" : s8%>
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" class="TD_LIST"
                align="center"><%=s9 == null ? "" : s9%>
            </td>
        </tr>
        <%
                }
                
                // 计算百分比
                DecimalFormat df = new DecimalFormat("#.##");
                String ngPercentage = "0.00";
                String bmPercentage = "0.00";
                
                if (totalCount > 0) {
                    ngPercentage = df.format((float)ngCount / totalCount * 100);
                    bmPercentage = df.format((float)bmCount / totalCount * 100);
                }
        %>
        <!-- 汇总信息 -->
        <tr>
            <td rowspan="3" class="TD_LIST_TITLE" align="center">汇总</td>
            <td colspan="2" class="TD_LIST_TITLE" align="center">总异常数量</td>
            <td class="TD_LIST_TITLE" align="center"><%=totalCount%></td>
            <td colspan="6" class="TD_LIST_TITLE"></td>
        </tr>
        <tr>
            <td colspan="2" class="TD_LIST_TITLE" align="center">NG码异常数量</td>
            <td class="TD_LIST_TITLE" align="center"><%=ngCount%></td>
            <td class="TD_LIST_TITLE" align="center">占比</td>
            <td colspan="5" class="TD_LIST_TITLE" align="center"><%=ngPercentage%>%</td>
        </tr>
        <tr>
            <td colspan="2" class="TD_LIST_TITLE" align="center">白码异常数量</td>
            <td class="TD_LIST_TITLE" align="center"><%=bmCount%></td>
            <td class="TD_LIST_TITLE" align="center">占比</td>
            <td colspan="5" class="TD_LIST_TITLE" align="center"><%=bmPercentage%>%</td>
        </tr>
        <%
            } else {
                session.removeAttribute("paging");
        %>
        <tr>
            <td height="100%" colspan="10" class="TD_LIST" valign="top">
                <div style="color: red;">无相关数据！</div>
            </td>
        </tr>
        <%
            }
        %>
        </tbody>
        <!-- 表体(end) -->
        <!-- 表尾(start) -->
        <tfoot style="display: table-footer-group;font-weight: bold;">
        <tr>
            <td colspan="10">
                <!-- 可以在这里添加表尾信息 -->
            </td>
        </tr>
        </tfoot>
        <!-- 表尾(end) -->
    </table>
</div>
</body>
</html> 
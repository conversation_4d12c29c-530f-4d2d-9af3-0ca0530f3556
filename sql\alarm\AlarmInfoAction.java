package com.wms.action.in;

import com.ricosoft.common.dao.dataSource.EntityDAO;
import com.ricosoft.common.logger.Logger;
import com.ricosoft.common.pagination.CommonPagination;
import com.ricosoft.common.pagination.PagingTool;
import com.ricosoft.common.tools.CommonTools;
import com.wms.action.AbstractAction;
import com.wms.hibernate.in.AlarmInfo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 *         说明：报警信息查询Action
 */
public class AlarmInfoAction extends AbstractAction {
    public void exec(HashMap hsSysParam, HashMap hsCurrentParam) throws Exception {
        HttpServletRequest request = (HttpServletRequest) hsCurrentParam.get("request");
        HttpServletResponse response = (HttpServletResponse) hsCurrentParam.get("response");
        HttpSession httpsession = request.getSession();
        EntityDAO dao = (EntityDAO) hsSysParam.get("dao");

        String user_id = (String) httpsession.getAttribute("user_id");
        String user_name = (String) httpsession.getAttribute("user_name");
        String method = CommonTools.getStrToGb2312(request.getParameter("method"));
        String flag = CommonTools.getStrToGb2312(request.getParameter("flag"));

        String start_data = CommonTools.getStrToGb2312(request.getParameter("start_data"));
        String end_data = CommonTools.getStrToGb2312(request.getParameter("end_data"));
        String alarmInfo = CommonTools.getStrToGb2312(request.getParameter("alarmInfo"));

        try {
            AlarmInfo alarmInfo = null;
            if (method.equals("search")) {
                if (flag.equals("1")) { // 查询统计
                    strUrl = "/jsp/in/alarm_info_list.jsp";

                    String strCountHQL = new AlarmInfo().getCountHQL(start_data, end_data);
                    String strQueryHQL = new AlarmInfo().getQueryHQL(start_data, end_data);

                    PagingTool pt = CommonPagination.getPagingTool(dao, strCountHQL, strQueryHQL, strUrl, 1, 1000);
                    List ls = pt.getLsResult();
                    request.setAttribute("exResultList", ls);
                    request.setAttribute("pagingTool", pt);
                    httpsession.setAttribute("paging", pt);
                } else if (flag.equals("2")) {
                    strUrl = "/jsp/in/alarm_info_list_report.jsp";

                    String strQueryHQL = new AlarmInfo().getQueryHQL(start_data, end_data);
                    String condition = start_data + "%7C" + end_data;
                    List ls = dao.searchEntities(strQueryHQL);
                    request.setAttribute("exResultList", ls);
                    request.setAttribute("condition", condition);
                }
            }
            request.getRequestDispatcher(strUrl).forward(request, response);
        } catch (Exception ex) {
            Logger.error(user_name + "执行：method=" + method + "，flag=" + flag + "方法时发生错误，错误代码：" + ex.getMessage());
            request.getRequestDispatcher("/error.jsp").forward(request, response);
        } finally {
            Logger.info(user_name + "执行：method=" + method + "，flag=" + flag + "方法");
        }
    }
}
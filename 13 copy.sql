CREATE VIEW [dbo].[v_scan_info] AS WITH 
-- 基础数据查询优化：使用更好的日期筛选
BaseDataCTE AS (
    SELECT 
        CONVERT(DATE, t.reserve2) AS scan_date,
        CONVERT(TIME, t.reserve2) AS scan_time,
        t.reserve2 AS scan_datetime,
        t.batchno,
        LEFT(t.producttime, 10) AS production_date,
        t.productid,
        t.traycode
    FROM 
        ocr_product_traycode_info_his t
    WHERE
        -- 优化为范围查询，更高效
        t.reserve2 >= '2024-07-01' 
),
-- 合并托盘扫描时间计算，计算每个托盘的运行时间
TrayDetailsCTE AS (
    SELECT 
        scan_date,
        batchno,
        traycode,
        production_date,
        MIN(scan_datetime) as first_scan_time,
        MAX(scan_datetime) as last_scan_time,
        -- 计算每个托盘的运行时间（分钟）
        DATEDIFF(MINUTE, MIN(scan_datetime), MAX(scan_datetime)) AS tray_running_minutes,
        CASE WHEN MAX(scan_time) <= '12:55:00' THEN 'AM' ELSE 'PM' END AS time_segment,
        COUNT(*) AS scan_count,
        SUM(CASE WHEN RIGHT(ISNULL(productid, ''), 2) <> 'NG' THEN 1 ELSE 0 END) AS tray_success_count,
        SUM(CASE WHEN RIGHT(ISNULL(productid, ''), 2) = 'NG' THEN 1 ELSE 0 END) AS tray_failure_count
    FROM BaseDataCTE
    GROUP BY 
        scan_date,
        batchno,
        traycode,
        production_date
),
-- 合并特殊托盘识别，减少CTE层级
SpecialTrayCTE AS (
    -- 上午第一托
    SELECT 
        scan_date, batchno, traycode, 'AM_First' AS tray_type
    FROM (
        SELECT 
            scan_date, batchno, traycode,
            ROW_NUMBER() OVER (PARTITION BY scan_date, batchno 
                              ORDER BY first_scan_time ASC) as rn
        FROM TrayDetailsCTE
        WHERE time_segment = 'AM'
    ) t
    WHERE rn = 1
    
    UNION ALL
    
    -- 上午最后一托
    SELECT 
        scan_date, batchno, traycode, 'AM_Last' AS tray_type
    FROM (
        SELECT 
            scan_date, batchno, traycode,
            ROW_NUMBER() OVER (PARTITION BY scan_date, batchno 
                              ORDER BY last_scan_time DESC) as rn
        FROM TrayDetailsCTE
        WHERE time_segment = 'AM'
    ) t
    WHERE rn = 1
    
    UNION ALL
    
    -- 下午第一托
    SELECT 
        scan_date, batchno, traycode, 'PM_First' AS tray_type
    FROM (
        SELECT 
            scan_date, batchno, traycode,
            ROW_NUMBER() OVER (PARTITION BY scan_date, batchno 
                              ORDER BY first_scan_time ASC) as rn
        FROM TrayDetailsCTE
        WHERE time_segment = 'PM'
    ) t
    WHERE rn = 1
    
    UNION ALL
    
    -- 下午最后一托
    SELECT 
        scan_date, batchno, traycode, 'PM_Last' AS tray_type
    FROM (
        SELECT 
            scan_date, batchno, traycode,
            ROW_NUMBER() OVER (PARTITION BY scan_date, batchno 
                              ORDER BY last_scan_time DESC) as rn
        FROM TrayDetailsCTE
        WHERE time_segment = 'PM'
    ) t
    WHERE rn = 1
),
-- 过滤掉特殊托盘
FilteredTraysCTE AS (
    SELECT t.*
    FROM TrayDetailsCTE t
    WHERE NOT EXISTS (
        SELECT 1 
        FROM SpecialTrayCTE s
        WHERE t.scan_date = s.scan_date 
          AND t.batchno = s.batchno 
          AND t.traycode = s.traycode
    )
),
-- 过滤掉特殊托盘的原始数据
FilteredDataCTE AS (
    SELECT b.*
    FROM BaseDataCTE b
    WHERE NOT EXISTS (
        SELECT 1 
        FROM SpecialTrayCTE s
        WHERE b.scan_date = s.scan_date 
          AND b.batchno = s.batchno 
          AND b.traycode = s.traycode
    )
),
-- 所有数据的聚合（累计每托运行时间）
FullAggregateCTE AS (
    SELECT 
        scan_date,
        batchno,
        production_date,
        MIN(first_scan_time) AS first_batch_time,
        MAX(last_scan_time) AS last_batch_time,
        MIN(CONVERT(TIME, first_scan_time)) AS min_scan_time,
        MAX(CONVERT(TIME, last_scan_time)) AS max_scan_time,
        SUM(scan_count) AS total_scan_count,
        SUM(tray_success_count) AS scan_success_count,
        SUM(tray_failure_count) AS scan_failure_count,
        COUNT(*) AS tray_code_count,
        -- 累计每托盘的运行时间
        SUM(tray_running_minutes) AS accumulated_tray_minutes
    FROM 
        TrayDetailsCTE
    GROUP BY 
        scan_date,
        batchno,
        production_date
),
-- 过滤后数据的聚合（累计每托运行时间）
FilteredAggregateCTE AS (
    SELECT 
        scan_date,
        batchno,
        production_date,
        MIN(first_scan_time) AS first_batch_time,
        MAX(last_scan_time) AS last_batch_time,
        MIN(CONVERT(TIME, first_scan_time)) AS min_scan_time,
        MAX(CONVERT(TIME, last_scan_time)) AS max_scan_time,
        SUM(scan_count) AS total_scan_count,
        SUM(tray_success_count) AS scan_success_count,
        SUM(tray_failure_count) AS scan_failure_count,
        COUNT(*) AS tray_code_count,
        -- 累计每托盘的运行时间
        SUM(tray_running_minutes) AS accumulated_tray_minutes
    FROM 
        FilteredTraysCTE
    GROUP BY 
        scan_date,
        batchno,
        production_date
),
-- 合并计算CTE，预计算所有时间差值，减少重复计算
ResultPreparationCTE AS (
    SELECT
        a.scan_date,
        a.batchno,
        a.production_date,
        a.min_scan_time,
        a.max_scan_time,
        a.total_scan_count,
        a.scan_success_count,
        a.scan_failure_count,
        a.tray_code_count,
        -- 使用累计的托盘运行时间
        a.accumulated_tray_minutes AS total_accumulated_tray_minutes,
        -- 也保留总时间差，用于显示
        DATEDIFF(MINUTE, a.min_scan_time, a.max_scan_time) AS total_running_time_minutes,
        
        f.min_scan_time AS f_min_scan_time,
        f.max_scan_time AS f_max_scan_time,
        f.total_scan_count AS f_total_scan_count,
        f.scan_success_count AS f_scan_success_count,
        f.scan_failure_count AS f_scan_failure_count,
        f.tray_code_count AS f_tray_code_count,
        -- 使用累计的托盘运行时间
        f.accumulated_tray_minutes AS filtered_accumulated_tray_minutes,
        -- 也保留总时间差，用于显示
        -- ISNULL(DATEDIFF(MINUTE, f.min_scan_time, f.max_scan_time), 0) AS filtered_running_time_minutes,
        
        -- 预计算差异值
        a.tray_code_count - ISNULL(f.tray_code_count, 0) AS removed_tray_count,
        a.accumulated_tray_minutes - ISNULL(f.accumulated_tray_minutes, 0) AS reduced_accumulated_minutes
    FROM 
        FullAggregateCTE a
        LEFT JOIN FilteredAggregateCTE f ON 
            a.scan_date = f.scan_date AND 
            a.batchno = f.batchno AND 
            a.production_date = f.production_date
    WHERE 
        a.tray_code_count > 0
)
-- 最终结果选择：使用累计的托盘运行时间计算效率
SELECT 
    -- 使用预计算的ID值，避免每行重复计算
    CONCAT(
        CONVERT(VARCHAR(10), r.scan_date, 23), '|', 
        ISNULL(r.batchno, ''), '|', 
        ISNULL(r.production_date, ''), '|',
        RIGHT('000000' + CAST(ROW_NUMBER() OVER (ORDER BY r.scan_date, r.batchno, r.production_date) AS VARCHAR(6)), 6)
    ) AS id,
    r.scan_date,
    CONVERT(VARCHAR(10), r.min_scan_time, 108) AS 'scan_time_start',
    CONVERT(VARCHAR(10), r.max_scan_time, 108) AS 'scan_time_end',
    r.batchno AS 'batch_number',
    r.production_date,
    r.total_scan_count,
    CAST(r.scan_success_count AS VARCHAR(10)) AS 'scan_success_count',
    CAST(r.scan_failure_count AS VARCHAR(10)) AS 'scan_failure_count',
    
    -- 使用预计算的指标提高性能
    CONVERT(VARCHAR(10), CAST(ROUND(
        CASE 
            WHEN r.total_scan_count = 0 THEN 0 
            ELSE r.scan_success_count * 100.0 / r.total_scan_count 
        END, 2) AS decimal(5,2)), 2) AS 'success_rate',
        
    CONVERT(VARCHAR(10), CAST(ROUND(
        CASE 
            WHEN r.total_scan_count = 0 THEN 0 
            ELSE r.scan_failure_count * 100.0 / r.total_scan_count 
        END, 2) AS decimal(5,2)), 2) AS 'error_code_rate',
        
    -- 保留原有的运行时间，用于显示
    r.total_running_time_minutes,
    
    -- 使用累计的托盘时间计算效率
    CONVERT(VARCHAR(10), CAST(ROUND(
        CASE 
            WHEN r.total_accumulated_tray_minutes = 0 THEN 0
            ELSE r.total_scan_count * 1.0 / r.total_accumulated_tray_minutes
        END, 2) AS decimal(10,2)), 2) AS 'total_daily_efficiency_per_minute',
        
    -- 使用累计的托盘时间计算每托盘效率
    CONVERT(VARCHAR(10), CAST(ROUND(
        CASE 
            WHEN r.tray_code_count = 0 THEN 0
            ELSE r.total_accumulated_tray_minutes * 1.0 / r.tray_code_count
        END, 2) AS decimal(10,2)), 2) AS 'total_daily_efficiency_per_tray_code_count',
        
    r.tray_code_count AS 'total_tray_code_count',
    r.total_accumulated_tray_minutes AS 'total_accumulated_tray_minutes',
    
    -- 过滤后的统计数据
    -- r.filtered_running_time_minutes, 
    ISNULL(r.f_tray_code_count, 0) AS 'filtered_tray_code_count', 
    ISNULL(r.filtered_accumulated_tray_minutes, 0) AS 'filtered_running_time_minutes',
    
    -- 使用累计的托盘时间计算过滤后的效率
    CONVERT(VARCHAR(10), CAST(ROUND(
        CASE 
            WHEN ISNULL(r.filtered_accumulated_tray_minutes, 0) = 0 THEN 0
            WHEN ISNULL(r.f_tray_code_count, 0) = 0 THEN 0
            ELSE r.filtered_accumulated_tray_minutes * 1.0 / r.f_tray_code_count
        END, 2) AS decimal(10,2)), 2) AS 'filtered_daily_efficiency_per_tray_code_count', 
    
    -- 差异统计
    r.removed_tray_count,
    r.reduced_accumulated_minutes AS 'reduced_accumulated_minutes'
FROM 
    ResultPreparationCTE r;
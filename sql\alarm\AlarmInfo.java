package com.wms.hibernate.in;

import com.ricosoft.common.dao.dataSource.EntityDAO;

import java.util.List;

/**
 * 报警信息表
 * 
 * <AUTHOR>
 *
 */
public class AlarmInfo {
    private String id;
    private String alarmId; /* 报警ID */
    private String dbAddress; /* 数据库地址 */
    private String alarmInfo; /* 报警信息 */
    private String startTime; /* 开始时间 */
    private String endTime; /* 结束时间 */
    private String durationSeconds; /* 持续时间(秒) */
    private String alarmCount; /* 报警次数 */
    private String durationFormatted; /* 格式化的持续时间 */

    public AlarmInfo() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAlarmId() {
        return alarmId;
    }

    public void setAlarmId(String alarmId) {
        this.alarmId = alarmId;
    }

    public String getDbAddress() {
        return dbAddress;
    }

    public void setDbAddress(String dbAddress) {
        this.dbAddress = dbAddress;
    }

    public String getAlarmInfo() {
        return alarmInfo;
    }

    public void setAlarmInfo(String alarmInfo) {
        this.alarmInfo = alarmInfo;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getDurationSeconds() {
        return durationSeconds;
    }

    public void setDurationSeconds(String durationSeconds) {
        this.durationSeconds = durationSeconds;
    }

    public String getAlarmCount() {
        return alarmCount;
    }

    public void setAlarmCount(String alarmCount) {
        this.alarmCount = alarmCount;
    }

    public String getDurationFormatted() {
        return durationFormatted;
    }

    public void setDurationFormatted(String durationFormatted) {
        this.durationFormatted = durationFormatted;
    }

    public String getQueryHQL(String start_data, String end_data) {
        StringBuffer sb = new StringBuffer();
        sb.append("from AlarmInfo where 1=1 ");
        if (start_data != null && !start_data.equals("")) {
            sb.append(" and startTime >= '" + start_data + " 00:00:00.000'");
        }
        if (end_data != null && !end_data.equals("")) {
            sb.append(" and startTime <= '" + end_data + " 23:59:59.999'");
        }
        sb.append(" order by startTime asc");
        return sb.toString();
    }

    public String getCountHQL(String start_data, String end_data) {
        StringBuffer sb = new StringBuffer();
        sb.append("select count(*) from AlarmInfo where 1=1 ");
        if (start_data != null && !start_data.equals("")) {
            sb.append(" and startTime >= '" + start_data + " 00:00:00.000'");
        }
        if (end_data != null && !end_data.equals("")) {
            sb.append(" and startTime <= '" + end_data + " 23:59:59.999'");
        }
        return sb.toString();
    }
}
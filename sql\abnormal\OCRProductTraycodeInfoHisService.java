package com.wms.service.ocr;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.Transaction;

import com.wms.hibernate.HibernateSessionFactory;
import com.wms.hibernate.ocr.OCRProductTraycodeInfoHis;

/**
 * OCR产品托盘码历史信息服务类
 */
public class OCRProductTraycodeInfoHisService {

    /**
     * 获取OCR异常数据列表
     * 
     * @param condition 查询条件
     * @return 异常数据列表
     * @throws Exception
     */
    public List<OCRProductTraycodeInfoHis> getOCRAbnormalList(String condition) throws Exception {
        List<OCRProductTraycodeInfoHis> list = new ArrayList<OCRProductTraycodeInfoHis>();
        Session session = null;
        Transaction tx = null;

        try {
            session = HibernateSessionFactory.getSession();
            tx = session.beginTransaction();

            StringBuilder hql = new StringBuilder();
            hql.append("from OCRProductTraycodeInfoHis where 1=1 ");

            // 添加查询条件
            if (condition != null && !condition.trim().equals("")) {
                hql.append(condition);
            }

            Query query = session.createQuery(hql.toString());
            list = query.list();

            tx.commit();
        } catch (Exception e) {
            if (tx != null) {
                tx.rollback();
            }
            throw new Exception("获取OCR异常数据列表失败：" + e.getMessage());
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }

        return list;
    }

    /**
     * 根据ID获取OCR产品托盘码历史信息
     * 
     * @param id ID
     * @return OCR产品托盘码历史信息
     * @throws Exception
     */
    public OCRProductTraycodeInfoHis getOCRProductTraycodeInfoHisById(String id) throws Exception {
        OCRProductTraycodeInfoHis info = null;
        Session session = null;
        Transaction tx = null;

        try {
            session = HibernateSessionFactory.getSession();
            tx = session.beginTransaction();

            info = (OCRProductTraycodeInfoHis) session.get(OCRProductTraycodeInfoHis.class, id);

            tx.commit();
        } catch (Exception e) {
            if (tx != null) {
                tx.rollback();
            }
            throw new Exception("根据ID获取OCR产品托盘码历史信息失败：" + e.getMessage());
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }

        return info;
    }

    /**
     * 保存OCR产品托盘码历史信息
     * 
     * @param info OCR产品托盘码历史信息
     * @throws Exception
     */
    public void saveOCRProductTraycodeInfoHis(OCRProductTraycodeInfoHis info) throws Exception {
        Session session = null;
        Transaction tx = null;

        try {
            session = HibernateSessionFactory.getSession();
            tx = session.beginTransaction();

            session.save(info);

            tx.commit();
        } catch (Exception e) {
            if (tx != null) {
                tx.rollback();
            }
            throw new Exception("保存OCR产品托盘码历史信息失败：" + e.getMessage());
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }

    /**
     * 更新OCR产品托盘码历史信息
     * 
     * @param info OCR产品托盘码历史信息
     * @throws Exception
     */
    public void updateOCRProductTraycodeInfoHis(OCRProductTraycodeInfoHis info) throws Exception {
        Session session = null;
        Transaction tx = null;

        try {
            session = HibernateSessionFactory.getSession();
            tx = session.beginTransaction();

            session.update(info);

            tx.commit();
        } catch (Exception e) {
            if (tx != null) {
                tx.rollback();
            }
            throw new Exception("更新OCR产品托盘码历史信息失败：" + e.getMessage());
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }

    /**
     * 删除OCR产品托盘码历史信息
     * 
     * @param id ID
     * @throws Exception
     */
    public void deleteOCRProductTraycodeInfoHis(String id) throws Exception {
        Session session = null;
        Transaction tx = null;

        try {
            session = HibernateSessionFactory.getSession();
            tx = session.beginTransaction();

            OCRProductTraycodeInfoHis info = (OCRProductTraycodeInfoHis) session.get(OCRProductTraycodeInfoHis.class,
                    id);
            if (info != null) {
                session.delete(info);
            }

            tx.commit();
        } catch (Exception e) {
            if (tx != null) {
                tx.rollback();
            }
            throw new Exception("删除OCR产品托盘码历史信息失败：" + e.getMessage());
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }
}
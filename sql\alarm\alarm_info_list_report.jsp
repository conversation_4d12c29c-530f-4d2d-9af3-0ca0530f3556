<%@ page contentType="text/html; charset=GB2312" %>
<%@ page import="java.util.List" %>
<%@ page import="com.wms.hibernate.in.AlarmInfo" %>
<%@ page import="java.text.DecimalFormat" %>
<%
    String condition = (String) request.getAttribute("condition");
%>
<html>
<head>
    <title>欢迎使用自动化立体仓库信息管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache">
    <meta http-equiv="expires" content="0">
    <meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
    <meta http-equiv="description" content="This is my page">
    <link href="<%=request.getContextPath()%>/js/Dialog18/style/default.css" rel="stylesheet" type="text/css">
    <link href="<%=request.getContextPath()%>/css/table.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="<%=request.getContextPath()%>/js/lodop/LodopFuncs.js"></script>
    <object id="LODOP_OB" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" width=0 height=0>
        <embed id="LODOP_EM" type="application/x-print-lodop" width=0 height=0></embed>
    </object>
    <style media=print>
        <!--
        .Noprint {
            display: none;
        }

        /*用本样式在打印时隐藏非打印项目*/
        .PageNext {
            page-break-after: always;
        }

        /*控制分页*/
        -->
    </style>
    <style type="text/css">
        thead {
            color: green;
            font-weight: bold;
            font-size: 14px;
        }

        tbody {
            color: blue;
            font-size: 12px;
        }

        tfoot {
            color: red;
        }
    </style>
    <script language="JavaScript">

        var ac = "<%=request.getContextPath()%>/service?actionCode=";
        var id = "<%=(String)request.getAttribute("id")%>";
        var HKEY_Root, HKEY_Path, HKEY_Key;
        HKEY_Root = "HKEY_CURRENT_USER";
        HKEY_Path = "\\Software\\Microsoft\\Internet Explorer\\PageSetup\\";

        //设置网页打印的页眉页脚
        function PageSetup_Null() {
            try {
                var Wsh = new ActiveXObject("WScript.Shell");
                HKEY_Key = "header";
                Wsh.RegWrite(HKEY_Root + HKEY_Path + HKEY_Key, "");
                HKEY_Key = "footer";
                Wsh.RegWrite(HKEY_Root + HKEY_Path + HKEY_Key, "&b第&p/&P页");
            } catch (e) {
            }
        }

        function DownFile() {
            window.location.href = ac + "DownFileAction&report_type=ReportAlarmInfo&file_type=excel&file_name=alarmInfo.xls&condition=<%=condition%>";
        }

        /*打印*/
        function Print() {
            PageSetup_Null();
            window.print();
        }

        function CreateTwoFormPage() {
            var LODOP; //声明为全局变量
            LODOP = getLodop();
            LODOP.PRINT_INIT("报警统计");
            LODOP.SET_PRINT_PAGESIZE(2, "210mm", "297mm", "BANCITJ");
            var strStyle = "<style> table,td,th {border-width: 0px;border-style: solid;}</style>";
            LODOP.ADD_PRINT_TABLE("5mm", "3mm", "280mm", "198mm", strStyle + document.getElementById("div1").innerHTML);
            LODOP.SET_SHOW_MODE("LANDSCAPE_DEFROTATED", 1);
            LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREWINDOW", 1);//打印后自动关闭预览窗口border-collapse: collapse
            LODOP.PREVIEW();
        };

    </script>
</head>

<body>

<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="Noprint">
    <tr>
        <td height="27">
            <OBJECT id=WebBrowser classid=CLSID:8856F961-340A-11D0-A96B-00C04FD705A2 height=0 width=0></OBJECT>
            <input onclick="DownFile()" type="button" name="button5" value="导出EXCEL" class="BUTTON_STYLE1">
            <input onclick="CreateTwoFormPage()" type="button" name="button5" value="打印" class="BUTTON_STYLE1">
            <input onclick="window.close()" type="button" name="button8" value="关闭" class="BUTTON_STYLE1">
        </td>
    </tr>
</table>

<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" class="Noprint">
    <tr>
        <td height="10">
            <hr align="center" color="#006699" noshade="noshade" size="1">
        </td>
    </tr>
</table>

<!-- 打印部分 -->
<div id="div1">
    <table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="MAIN_REPORT">
        <thead>
        <!-- 表头(start) -->
        <tr>
            <td colspan="9">
                <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" bordercolor="#FFFFFF">
                    <tr>
                        <td height="50" valign="top" align="center">
                            <div width="98%" align="center" style="display: table-header-group;  font-size: 24px;">&nbsp;&nbsp;报 警 信 息 查 询
                                <hr align="center" color="#006699" noshade="noshade" size="1" width="1800">
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr style="height: 30px; ">
            <td style="border:1px solid #000000" class="TD_LIST_TITLE" align="center" width="50">行号</td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center"  width="100">报警ID
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center" width="120">数据库地址
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center" width="200">报警信息
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center" width="150">开始时间
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center" width="150">结束时间
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center" width="100">持续时间(秒)
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center" width="80">报警次数
            </td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000;border-top: 1px solid #000000"
                class="TD_LIST_TITLE" align="center" width="100">持续时间
            </td>
        </tr>
        </thead>
        <tbody>
        <%
            List ls = (List) request.getAttribute("exResultList");

            if (ls != null && ls.size() > 0) {
                AlarmInfo alarmInfo = null;
                String alarmId, dbAddress, alarmInfoText, startTime, endTime, durationSeconds, alarmCount, durationFormatted;
                
                for (int i = 0; i < ls.size(); i++) {
                    alarmInfo = (AlarmInfo) ls.get(i);

                    alarmId = alarmInfo.getAlarmId();
                    dbAddress = alarmInfo.getDbAddress();
                    alarmInfoText = alarmInfo.getAlarmInfo();
                    startTime = alarmInfo.getStartTime();
                    endTime = alarmInfo.getEndTime();
                    durationSeconds = alarmInfo.getDurationSeconds();
                    alarmCount = alarmInfo.getAlarmCount();
                    durationFormatted = alarmInfo.getDurationFormatted();
        %>
        <tr style="height: 30px;">
            <td style="border-left: 1px solid #000000;border-right: 1px solid #000000;border-bottom: 1px solid #000000" align="center"><%=i + 1%></td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" align="center"><%=alarmId == null ? "" : alarmId%></td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" align="center"><%=dbAddress == null ? "" : dbAddress%></td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" align="center"><%=alarmInfoText == null ? "" : alarmInfoText%></td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" align="center"><%=startTime == null ? "" : startTime%></td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" align="center"><%=endTime == null ? "" : endTime%></td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" align="center"><%=durationSeconds == null ? "" : durationSeconds%></td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" align="center"><%=alarmCount == null ? "" : alarmCount%></td>
            <td style="border-right: 1px solid #000000;border-bottom: 1px solid #000000" align="center"><%=durationFormatted == null ? "" : durationFormatted%></td>
        </tr>
        <%
                }
            }
        %>
        </tbody>
    </table>
</div>

</body>
</html> 
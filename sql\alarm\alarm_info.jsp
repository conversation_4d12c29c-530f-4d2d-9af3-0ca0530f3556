<%@ page contentType="text/html; charset=GB2312" %>
<%@ page import="java.util.Date" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    Date date = new Date();
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    String currentDate = sdf.format(date);
%>
<html>
<head>
    <title>自动化立体仓库信息管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache">
    <meta http-equiv="expires" content="0">
    <link href="<%=request.getContextPath()%>/css/style.css" rel="stylesheet" type="text/css">
    <link href="<%=request.getContextPath()%>/css/table.css" rel="stylesheet" type="text/css">
    <link href="<%=request.getContextPath()%>/js/Dialog18/style/default.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="<%=request.getContextPath()%>/js/Dialog18/js/Dialog.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/js/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        var ac = "<%=request.getContextPath()%>/service?actionCode=";

        function OnLoad() {
            parent.RemoveLoading();
        }

        function search(flag) {
            var start_data = document.getElementById("start_data").value;
            var end_data = document.getElementById("end_data").value;
            
            if (start_data == "") {
                Dialog.alert("请选择开始日期！");
                return;
            }
            if (end_data == "") {
                Dialog.alert("请选择结束日期！");
                return;
            }
            
            var url = ac + "AlarmInfoAction&method=search&flag=" + flag + "&start_data=" + start_data + "&end_data=" + end_data;
            if (flag == "1") {
                parent.mainFrame.location.href = url;
            } else if (flag == "2") {
                window.open(url, "_blank", "width=1000,height=600,scrollbars=yes,location=no");
            }
        }
    </script>
</head>

<body onLoad="OnLoad()">
<table width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="MAIN_TABLE">
    <tr>
        <td height="22" class="TD_TITLE" align="center">报警信息查询</td>
    </tr>
    <tr>
        <td height="1" class="TD_SPLIT_LINE"></td>
    </tr>
    <tr>
        <td height="100%" valign="top">
            <table width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
                <tr>
                    <td height="100%" valign="top">
                        <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="TABLE_FORM">
                            <tr>
                                <td width="10%" class="TD_FORM_TITLE" align="right">开始日期：</td>
                                <td width="15%" class="TD_FORM_TEXT">
                                    <input type="text" id="start_data" name="start_data" class="INPUT_TEXT" value="<%=currentDate%>" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd'})">
                                </td>
                                <td width="10%" class="TD_FORM_TITLE" align="right">结束日期：</td>
                                <td width="15%" class="TD_FORM_TEXT">
                                    <input type="text" id="end_data" name="end_data" class="INPUT_TEXT" value="<%=currentDate%>" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd'})">
                                </td>
                                <td width="50%" class="TD_FORM_TEXT" align="left">
                                    <input type="button" value="查询" class="BUTTON_STYLE1" onclick="search('1')">
                                    <input type="button" value="打印" class="BUTTON_STYLE1" onclick="search('2')">
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
</body>
</html> 
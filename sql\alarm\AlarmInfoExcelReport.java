package com.wms.action.report.save;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.*;

import com.wms.hibernate.in.AlarmInfo;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

/**
 * 报警信息查询报表
 */
public class AlarmInfoExcelReport {
    /**
     * 功能:EXCEL 报表
     * 
     * @throws Exception
     */
    public static void excelReport(String filePath, List<AlarmInfo> ls) throws Exception {
        try {
            File f = new File(filePath);
            if (f.exists()) { // 存在就删除
                f.delete();
            }
            f.createNewFile(); // 创建excel文件

            OutputStream out = new FileOutputStream(f);
            WritableWorkbook wwb = Workbook.createWorkbook(out);
            WritableSheet ws = wwb.createSheet("第一页", 0); // 创建 Excel工作表 指定名称和位置

            // 字体，大小，颜色
            WritableFont wf1 = new WritableFont(WritableFont.createFont("宋体_GB2312"), 18, WritableFont.NO_BOLD);
            WritableFont wf2 = new WritableFont(WritableFont.createFont("宋体_GB2312"), 12, WritableFont.NO_BOLD);

            // 单元格格式
            WritableCellFormat wcf1 = new WritableCellFormat(wf1);
            wcf1.setAlignment(Alignment.CENTRE); // 水平对齐方式指定为居中
            wcf1.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐方式指定为居中

            WritableCellFormat wcf2 = new WritableCellFormat(wf2);
            wcf2.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐方式指定为居中
            wcf2.setAlignment(Alignment.CENTRE); // 水平对齐方式指定为居中
            wcf2.setWrap(true); // 设置自动换行
            wcf2.setBorder(Border.ALL, BorderLineStyle.THIN); // 设置细边框

            ws.mergeCells(0, 0, 8, 0); // 合并单元格（参数为：起始列、起始行、结束列、结束行）
            Label label = new Label(0, 0, "报警信息查询", wcf1);
            ws.addCell(label);

            // 设置列标题
            label = new Label(0, 1, "序号", wcf2);
            ws.addCell(label);

            label = new Label(1, 1, "报警ID", wcf2);
            ws.addCell(label);

            label = new Label(2, 1, "数据库地址", wcf2);
            ws.addCell(label);

            label = new Label(3, 1, "报警信息", wcf2);
            ws.addCell(label);

            label = new Label(4, 1, "开始时间", wcf2);
            ws.addCell(label);

            label = new Label(5, 1, "结束时间", wcf2);
            ws.addCell(label);

            label = new Label(6, 1, "持续时间(秒)", wcf2);
            ws.addCell(label);

            label = new Label(7, 1, "报警次数", wcf2);
            ws.addCell(label);

            label = new Label(8, 1, "持续时间", wcf2);
            ws.addCell(label);

            ws.setColumnView(0, 10);
            ws.setColumnView(1, 15);
            ws.setColumnView(2, 15);
            ws.setColumnView(3, 30);
            ws.setColumnView(4, 20);
            ws.setColumnView(5, 20);
            ws.setColumnView(6, 15);
            ws.setColumnView(7, 15);
            ws.setColumnView(8, 15); // 设置列宽

            int row = 2; // 从第3行开始
            if (ls != null) {
                AlarmInfo alarmInfo = null;
                for (int i = 0; i < ls.size(); i++) {
                    alarmInfo = ls.get(i);

                    label = new Label(0, row, (i + 1) + "", wcf2);
                    ws.addCell(label);

                    label = new Label(1, row, alarmInfo.getAlarmId(), wcf2);
                    ws.addCell(label);

                    label = new Label(2, row, alarmInfo.getDbAddress(), wcf2);
                    ws.addCell(label);

                    label = new Label(3, row, alarmInfo.getAlarmInfo(), wcf2);
                    ws.addCell(label);

                    label = new Label(4, row, alarmInfo.getStartTime(), wcf2);
                    ws.addCell(label);

                    label = new Label(5, row, alarmInfo.getEndTime(), wcf2);
                    ws.addCell(label);

                    label = new Label(6, row, alarmInfo.getDurationSeconds(), wcf2);
                    ws.addCell(label);

                    label = new Label(7, row, alarmInfo.getAlarmCount(), wcf2);
                    ws.addCell(label);

                    label = new Label(8, row, alarmInfo.getDurationFormatted(), wcf2);
                    ws.addCell(label);

                    row++;
                }
            }

            wwb.write(); // 写入数据
            wwb.close();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
}
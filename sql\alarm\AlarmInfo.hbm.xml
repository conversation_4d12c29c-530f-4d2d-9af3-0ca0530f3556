<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping 
	PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd"
	> 

<hibernate-mapping>
	<class name="com.wms.hibernate.in.AlarmInfo" table="v_alarm_event_statistics" dynamic-update="true" dynamic-insert="true">
		<id name="id" type="string">
            <column name="id" sql-type="string" not-null="true"/>
        </id>
		<property name="alarmId" type="string" not-null="true">
			<column name="alarm_id" sql-type="string" not-null="true"/>
        </property>
		<property name="dbAddress">
			<column name="db_address" sql-type="string" not-null="true"/>
		</property>
		<property name="alarmInfo">
			<column name="alarm_info" sql-type="string" not-null="true"/>
		</property>
		<property name="startTime">
			<column name="start_time" sql-type="datetime" not-null="true"/>
		</property>
		<property name="endTime">
			<column name="end_time" sql-type="datetime" not-null="true"/>
		</property>
		<property name="durationSeconds">
			<column name="duration_seconds" sql-type="int" not-null="true"/>
		</property>
		<property name="alarmCount">
			<column name="alarm_count" sql-type="int" not-null="true"/>
		</property>
		<property name="durationFormatted">
			<column name="duration_formatted" sql-type="string" not-null="true"/>
		</property>
	</class>
</hibernate-mapping> 
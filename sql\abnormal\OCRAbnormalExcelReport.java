package com.wms.action.report.save;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.*;

import com.wms.hibernate.ocr.OCRProductTraycodeInfoHis;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

/**
 * OCR异常数据报表
 */
public class OCRAbnormalExcelReport {
    /**
     * 功能:EXCEL 报表
     * 
     * @throws Exception
     */
    public static void excelReport(String filePath, List<OCRProductTraycodeInfoHis> ls) throws Exception {
        try {
            File f = new File(filePath);
            if (f.exists()) { // 存在就删除
                f.delete();
            }
            f.createNewFile(); // 创建excel文件

            OutputStream out = new FileOutputStream(f);
            WritableWorkbook wwb = Workbook.createWorkbook(out);
            WritableSheet ws = wwb.createSheet("第一页", 0);   //创建 Excel工作表 指定名称和位置

            // 字体，大小，颜色
            WritableFont wf1 = new WritableFont(WritableFont.createFont("宋体_GB2312"), 18, WritableFont.NO_BOLD);
            WritableFont wf2 = new WritableFont(WritableFont.createFont("宋体_GB2312"), 12, WritableFont.NO_BOLD);

            // 单元格格式
            WritableCellFormat wcf1 = new WritableCellFormat(wf1);
            wcf1.setAlignment(Alignment.CENTRE); // 水平对齐方式指定为居中
            wcf1.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐方式指定为居中

            WritableCellFormat wcf2 = new WritableCellFormat(wf2);
            wcf2.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐方式指定为居中
            wcf2.setAlignment(Alignment.CENTRE); // 水平对齐方式指定为居中
            wcf2.setWrap(true); // 设置自动换行
            wcf2.setBorder(Border.ALL, BorderLineStyle.THIN); // 设置细边框

            ws.mergeCells(0, 0, 9, 0); // 合并单元格（参数为：起始列、起始行、结束列、结束行）
            Label label = new Label(0, 0, "OCR异常数据报表", wcf1);
            ws.addCell(label);

            // 设置列标题
            label = new Label(0, 1, "序号", wcf2);
            ws.addCell(label);
            
            label = new Label(1, 1, "箱码", wcf2);
            ws.addCell(label);
            
            label = new Label(2, 1, "产品编码", wcf2);
            ws.addCell(label);
            
            label = new Label(3, 1, "产品名称", wcf2);
            ws.addCell(label);
            
            label = new Label(4, 1, "批次", wcf2);
            ws.addCell(label);
            
            label = new Label(5, 1, "产地编码", wcf2);
            ws.addCell(label);
            
            label = new Label(6, 1, "单位", wcf2);
            ws.addCell(label);
            
            label = new Label(7, 1, "添加时间", wcf2);
            ws.addCell(label);
            
            label = new Label(8, 1, "线体", wcf2);
            ws.addCell(label);
            
            label = new Label(9, 1, "异常类型", wcf2);
            ws.addCell(label);

            // 设置列宽
            ws.setColumnView(0, 10);  // 序号
            ws.setColumnView(1, 60);  // 箱码
            ws.setColumnView(2, 25);  // 产品编码
            ws.setColumnView(3, 15);  // 产品名称
            ws.setColumnView(4, 20);  // 批次
            ws.setColumnView(5, 20);  // 产地编码
            ws.setColumnView(6, 15);  // 单位
            ws.setColumnView(7, 25);  // 添加时间
            ws.setColumnView(8, 15);  // 线体
            ws.setColumnView(9, 20);  // 异常类型

            int row = 2; // 从第3行开始
            int ngCount = 0;
            int bmCount = 0;
            int totalCount = 0;
            
            if (ls != null && ls.size() > 0) {
                OCRProductTraycodeInfoHis data = null;
                String s0, s1, s2, s3, s4, s5, s6, s7, s8, s9 = null;
                String product_id = "";
                
                totalCount = ls.size();
                
                for (int i = 0; i < ls.size(); i++) {
                    data = ls.get(i);
                    
                    s0 = data.getBoxno();
                    product_id = data.getProductid();
                    if (product_id.contains("BM")) {
                        s1 = data.getProductid();
                        s2 = "白码";
                        s9 = "白码异常";
                        bmCount++;
                    } else {
                        s1 = data.getProductid();
                        s2 = "NG码";
                        s9 = "NG码异常";
                        ngCount++;
                    }
                    s3 = data.getBatchno();
                    s4 = data.getZbatchno();
                    s5 = data.getUnit();
                    s6 = data.getReserve2();
                    s7 = data.getReserve5();
                    if (s7.equals("192.168.11.11")) {
                        s8 = "A线";
                    } else if (s7.equals("192.168.11.12")) {
                        s8 = "B线";
                    } else {
                        s8 = "未知";
                    }

                    label = new Label(0, row, (i + 1) + "", wcf2);
                    ws.addCell(label);

                    label = new Label(1, row, s0 == null ? "" : s0, wcf2);
                    ws.addCell(label);

                    label = new Label(2, row, s1 == null ? "" : s1, wcf2);
                    ws.addCell(label);

                    label = new Label(3, row, s2 == null ? "" : s2, wcf2);
                    ws.addCell(label);

                    label = new Label(4, row, s3 == null ? "" : s3, wcf2);
                    ws.addCell(label);

                    label = new Label(5, row, s4 == null ? "" : s4, wcf2);
                    ws.addCell(label);

                    label = new Label(6, row, s5 == null ? "" : s5, wcf2);
                    ws.addCell(label);

                    label = new Label(7, row, s6 == null ? "" : s6, wcf2);
                    ws.addCell(label);

                    label = new Label(8, row, s8 == null ? "" : s8, wcf2);
                    ws.addCell(label);
                    
                    label = new Label(9, row, s9 == null ? "" : s9, wcf2);
                    ws.addCell(label);

                    row++;
                }
                
                // 计算百分比
                DecimalFormat df = new DecimalFormat("#.##");
                String ngPercentage = "0.00";
                String bmPercentage = "0.00";
                
                if (totalCount > 0) {
                    ngPercentage = df.format((float)ngCount / totalCount * 100);
                    bmPercentage = df.format((float)bmCount / totalCount * 100);
                }
                
                // 添加汇总信息
                row++;
                label = new Label(0, row, "汇总", wcf2);
                ws.addCell(label);
                ws.mergeCells(0, row, 0, row+2); // 合并单元格（参数为：起始列、起始行、结束列、结束行）
                
                label = new Label(1, row, "总异常数量", wcf2);
                ws.addCell(label);
                ws.mergeCells(1, row, 2, row);
                
                label = new Label(3, row, totalCount + "", wcf2);
                ws.addCell(label);
                
                row++;
                label = new Label(1, row, "NG码异常数量", wcf2);
                ws.addCell(label);
                ws.mergeCells(1, row, 2, row);
                
                label = new Label(3, row, ngCount + "", wcf2);
                ws.addCell(label);
                
                label = new Label(4, row, "占比", wcf2);
                ws.addCell(label);
                
                label = new Label(5, row, ngPercentage + "%", wcf2);
                ws.addCell(label);
                ws.mergeCells(5, row, 9, row);
                
                row++;
                label = new Label(1, row, "白码异常数量", wcf2);
                ws.addCell(label);
                ws.mergeCells(1, row, 2, row);
                
                label = new Label(3, row, bmCount + "", wcf2);
                ws.addCell(label);
                
                label = new Label(4, row, "占比", wcf2);
                ws.addCell(label);
                
                label = new Label(5, row, bmPercentage + "%", wcf2);
                ws.addCell(label);
                ws.mergeCells(5, row, 9, row);
            }
            
            wwb.write();
            wwb.close();
        } catch (Exception er) {
            throw new Exception("生成OCR异常数据EXCEL报表失败:" + er.getMessage());
        }
    }
} 
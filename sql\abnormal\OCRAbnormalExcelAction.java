package com.wms.action.report;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.struts.action.Action;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;

import com.wms.action.report.save.OCRAbnormalExcelReport;
import com.wms.hibernate.ocr.OCRProductTraycodeInfoHis;
import com.wms.service.ocr.OCRProductTraycodeInfoHisService;

/**
 * OCR异常数据Excel导出Action
 */
public class OCRAbnormalExcelAction extends Action {

    @Override
    public ActionForward execute(ActionMapping mapping, ActionForm form, HttpServletRequest request,
            HttpServletResponse response) throws Exception {
        HttpSession session = request.getSession();
        String condition = request.getParameter("condition");
        String fileName = request.getParameter("file_name");

        try {
            // 获取OCR异常数据列表
            OCRProductTraycodeInfoHisService service = new OCRProductTraycodeInfoHisService();
            List<OCRProductTraycodeInfoHis> list = service.getOCRAbnormalList(condition);

            // 生成Excel文件路径
            String filePath = session.getServletContext().getRealPath("/") + "temp/" + fileName;

            // 生成Excel文件
            OCRAbnormalExcelReport.excelReport(filePath, list);

            // 下载文件
            File file = new File(filePath);
            if (file.exists()) {
                response.reset();
                response.setContentType("application/octet-stream");
                response.addHeader("Content-Disposition",
                        "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
                response.addHeader("Content-Length", "" + file.length());

                OutputStream out = response.getOutputStream();
                InputStream in = new FileInputStream(file);

                byte[] buffer = new byte[1024];
                int len = 0;
                while ((len = in.read(buffer)) > 0) {
                    out.write(buffer, 0, len);
                }

                in.close();
                out.close();

                // 删除临时文件
                file.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("back_msg", "导出OCR异常数据Excel失败：" + e.getMessage());
            return mapping.findForward("error");
        }

        return null;
    }
}
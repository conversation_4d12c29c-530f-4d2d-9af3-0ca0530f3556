<%@ page contentType="text/html; charset=GB2312" %>
<%@ page import="java.util.List" %>
<%@ page import="com.wms.hibernate.in.AlarmInfo" %>
<%@ page import="java.text.DecimalFormat" %>
<html>
<head>
    <title>自动化立体仓库信息管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache">
    <meta http-equiv="expires" content="0">
    <link href="<%=request.getContextPath()%>/css/style.css" rel="stylesheet" type="text/css">
    <link href="<%=request.getContextPath()%>/css/table.css" rel="stylesheet" type="text/css">
    <script type="text/javascript">
        var ac = "<%=request.getContextPath()%>/service?actionCode=";

        function OnLoad() {
            parent.RemoveLoading();
            parent.page.location.reload();

            var back_msg = "<%=request.getAttribute("back_msg")==null?"":(String)request.getAttribute("back_msg")%>";
            if (back_msg != "") {
                if (back_msg == "Y") {
                    alert("操作成功！");
                } else {
                    alert(back_msg);
                }
            }
        }
    </script>
</head>

<body onLoad="OnLoad()">
<table id="tb" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="MAIN_TABLE">
    <tr>
        <td class="TD_LIST_TITLE" align="center" width="50">行号</td>
        <td class="TD_LIST_TITLE" align="center">报警ID</td>
        <td class="TD_LIST_TITLE" align="center">数据库地址</td>
        <td class="TD_LIST_TITLE" align="center">报警信息</td>
        <td class="TD_LIST_TITLE" align="center">开始时间</td>
        <td class="TD_LIST_TITLE" align="center">结束时间</td>
        <td class="TD_LIST_TITLE" align="center">持续时间(秒)</td>
        <td class="TD_LIST_TITLE" align="center">报警次数</td>
        <td class="TD_LIST_TITLE" align="center">持续时间</td>
    </tr>
    <%
        List ls = (List) request.getAttribute("exResultList");

        if (ls != null && ls.size() > 0) {
            AlarmInfo alarmInfo = null;
            String alarmId, dbAddress, alarmInfoText, startTime, endTime, durationSeconds, alarmCount, durationFormatted;
            
            for (int i = 0; i < ls.size(); i++) {
                alarmInfo = (AlarmInfo) ls.get(i);

                alarmId = alarmInfo.getAlarmId();
                dbAddress = alarmInfo.getDbAddress();
                alarmInfoText = alarmInfo.getAlarmInfo();
                startTime = alarmInfo.getStartTime();
                endTime = alarmInfo.getEndTime();
                durationSeconds = alarmInfo.getDurationSeconds();
                alarmCount = alarmInfo.getAlarmCount();
                durationFormatted = alarmInfo.getDurationFormatted();
    %>
    <tr onmouseover="this.bgColor='#E2E8EA'" onmouseout="this.bgColor=''">
        <td class="TD_LIST" align="center"><%=i + 1%></td>
        <td class="TD_LIST" align="center"><%=alarmId == null ? "" : alarmId%></td>
        <td class="TD_LIST" align="center"><%=dbAddress == null ? "" : dbAddress%></td>
        <td class="TD_LIST" align="center"><%=alarmInfoText == null ? "" : alarmInfoText%></td>
        <td class="TD_LIST" align="center"><%=startTime == null ? "" : startTime%></td>
        <td class="TD_LIST" align="center"><%=endTime == null ? "" : endTime%></td>
        <td class="TD_LIST" align="center"><%=durationSeconds == null ? "" : durationSeconds%></td>
        <td class="TD_LIST" align="center"><%=alarmCount == null ? "" : alarmCount%></td>
        <td class="TD_LIST" align="center"><%=durationFormatted == null ? "" : durationFormatted%></td>
    </tr>
    <%
        }
    } else {
        session.removeAttribute("paging");
    }
    %>
    <tr>
        <td height="100%" colspan="9" class="TD_LIST" valign="top">
            <div style="color: red;"><%if (ls != null && ls.size() == 0) {%>无相关数据！<%}%></div>
        </td>
    </tr>
</table>

</body>
</html> 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping 
	PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd"
	> 

<hibernate-mapping>
	<class name="com.wms.hibernate.in.ScanInfo" table="v_scan_info" dynamic-update="true" dynamic-insert="true">
		<id name="id" type="string">
            <column name="id" sql-type="string" not-null="true"/>
        </id>
		<property name="scanDate" type="string" not-null="true">
			<column name="scan_date" sql-type="string" not-null="true"/>
        </property>
		<property name="scanTimeStart">
			<column name="scan_time_start" sql-type="int" not-null="true"/>
		</property>
		<property name="scanTimeEnd">
			<column name="scan_time_end" sql-type="string" not-null="true"/>
		</property>
		<property name="batchNumber">
			<column name="batch_number" sql-type="string" not-null="false"/>
		</property>
		<property name="productionDate">
			<column name="production_date" sql-type="string" not-null="true"/>
		</property>
		<property name="totalScanCount">
			<column name="total_scan_count" sql-type="string" not-null="false"/>
		</property>
		<property name="scanSuccessCount">
			<column name="scan_success_count" sql-type="string" not-null="false"/>
		</property>
		<property name="scanFailureCount">
			<column name="scan_failure_count" sql-type="string" not-null="false"/>
		</property>
		<property name="successRate">
			<column name="success_rate" sql-type="string" not-null="false"/>
		</property>
		<property name="errorCodeRate">
			<column name="error_code_rate" sql-type="string" not-null="false"/>
		</property>
<!--		<property name="runningTimeMinutes">-->
<!--			<column name="running_time_minutes" sql-type="string" not-null="false"/>-->
<!--		</property>-->
<!--		<property name="dailyEfficiencyPerMinute">-->
<!--			<column name="daily_efficiency_per_minute" sql-type="string" not-null="false"/>-->
<!--		</property>-->
<!--		<property name="dailyEfficiencyPerTrayCodeCount">-->
<!--			<column name="daily_efficiency_per_tray_code_count" sql-type="string" not-null="false"/> &lt;!&ndash;每板多少分钟&ndash;&gt;-->
<!--		</property>-->
<!--		<property name="trayCodeCount">-->
<!--			<column name="tray_code_count" sql-type="string" not-null="false"/> &lt;!&ndash;板数&ndash;&gt;-->
<!--		</property>-->
		<property name="filteredRunningTimeMinutes">
			<column name="filtered_running_time_minutes" sql-type="string" not-null="false"/> <!--过滤后的运行时间（分钟）-->
		</property>
		<property name="filteredTrayCodeCount">
			<column name="filtered_tray_code_count" sql-type="string" not-null="false"/> <!--过滤后的板数-->
		</property>	
		<property name="filteredDailyEfficiencyPerTrayCodeCount">
			<column name="filtered_daily_efficiency_per_tray_code_count" sql-type="string" not-null="false"/> <!--过滤后的平均效率（分钟/板）-->
		</property>
<!--		<property name="TotalRunningTimeMinutes">-->
<!--			<column name="total_running_time_minutes" sql-type="string" not-null="false"/>-->
<!--		</property>-->
<!--		<property name="averageEfficiencyPerMinute">-->
<!--			<column name="average_efficiency_per_minute" sql-type="string" not-null="false"/>-->
<!--		</property>-->
<!--		<property name="TotalRunningTimeMinutes" formula="CONVERT(VARCHAR(10),SUM(running_time_minutes) OVER ())"/>-->
<!--		<property name="averageEfficiencyPerMinute" formula="CAST(ROUND(SUM(CAST(total_scan_count AS decimal(10,2))) OVER () * 1.0 /  NULLIF(SUM(running_time_minutes) OVER (), 0), 2) AS decimal(10,2)),2)"/>-->
<!--		scan_bm_count-->
<!--		bm_code_rate-->

<!--		&#45;&#45; 添加二维码等级统计输出-->
<!--		CAST(r.total_level_a_count AS VARCHAR(10)) AS 'level_a_count',-->
<!--		CAST(r.total_level_b_count AS VARCHAR(10)) AS 'level_b_count',-->
<!--		CAST(r.total_level_c_count AS VARCHAR(10)) AS 'level_c_count',-->
<!--		CAST(r.total_level_d_count AS VARCHAR(10)) AS 'level_d_count',-->
<!--		CAST(r.total_level_e_count AS VARCHAR(10)) AS 'level_e_count',-->
<!--		CAST(r.total_level_g_count AS VARCHAR(10)) AS 'level_g_count',-->
<!--		CAST(r.total_level_m_count AS VARCHAR(10)) AS 'level_m_count'-->

		<property name="scanBmCount">
			<column name="scan_bm_count" sql-type="string" not-null="false"/> <!--白码数量-->
		</property>
		<property name="bmCodeRate">
			<column name="bm_code_rate" sql-type="string" not-null="false"/> <!--白码率-->
		</property>
		<property name="levelACount">
			<column name="level_a_count" sql-type="string" not-null="false"/> <!--二维码等级 A-->
		</property>
		<property name="levelBCount">
			<column name="level_b_count" sql-type="string" not-null="false"/> <!--二维码等级 B-->
		</property>
		<property name="levelCCount">
			<column name="level_c_count" sql-type="string" not-null="false"/> <!--二维码等级 C-->
		</property>
		<property name="levelDCount">
			<column name="level_d_count" sql-type="string" not-null="false"/> <!--二维码等级 D-->
		</property>
		<property name="levelECount">
			<column name="level_e_count" sql-type="string" not-null="false"/> <!--二维码等级 E-->
		</property>
		<property name="levelGCount">
			<column name="level_g_count" sql-type="string" not-null="false"/> <!--二维码等级 G-->
		</property>
		<property name="levelMCount">
			<column name="level_m_count" sql-type="string" not-null="false"/> <!--二维码等级 M-->
		</property>
	</class>
</hibernate-mapping>


USE [DATA]
GO

/****** Object:  View [dbo].[v_scan_info]    Script Date: 2025/3/28 15:31:59 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[v_scan_info] AS WITH SubqueryCTE AS (
    SELECT 
        CONVERT(DATE, t.reserve2) AS scan_date,
        CONVERT(TIME, t.reserve2) AS scan_time,
        t.batchno,
        LEFT(t.producttime, 10) AS production_date,
        t.productid,
        t.traycode
    FROM 
        ocr_product_traycode_info_his t
    WHERE
    -- 筛选当前月的数据
    -- YEAR(t.reserve2) = YEAR('2024') AND
    -- MONTH(t.reserve2) = MONTH('07')
		
		(YEAR(t.reserve2) > 2024 OR 
 (YEAR(t.reserve2) = 2024 AND MONTH(t.reserve2) >= 7))
),
TrayLastScanCTE AS (
    SELECT 
        scan_date,
        batchno,
        traycode,
        MAX(scan_time) as last_scan_time,
        CASE WHEN MAX(scan_time) <= '12:55:00' THEN 'AM' ELSE 'PM' END AS time_segment
    FROM SubqueryCTE
    GROUP BY 
        scan_date,
        batchno,
        traycode
),
LastTrayCTE AS (
    SELECT 
        scan_date,
        batchno,
        traycode
    FROM (
        SELECT 
            scan_date,
            batchno,
            traycode,
            ROW_NUMBER() OVER (
                PARTITION BY scan_date, batchno, time_segment 
                ORDER BY last_scan_time DESC
            ) as rn
        FROM TrayLastScanCTE
    ) t
    WHERE rn = 1
),
FilteredCTE AS (
    SELECT s.*
    FROM SubqueryCTE s
    LEFT JOIN LastTrayCTE l ON 
        s.scan_date = l.scan_date AND 
        s.batchno = l.batchno AND 
        s.traycode = l.traycode
    WHERE l.traycode IS NULL
),
AggregateCTE AS (
    SELECT 
        scan_date,
        batchno,
        production_date,
        MIN(scan_time) AS min_scan_time,
        MAX(scan_time) AS max_scan_time,
        COUNT(*) AS total_scan_count,
        SUM(CASE WHEN RIGHT(productid, 2) <> 'NG' THEN 1 ELSE 0 END) AS scan_success_count,
        SUM(CASE WHEN RIGHT(productid, 2) = 'NG' THEN 1 ELSE 0 END) AS scan_failure_count,
        COUNT(DISTINCT traycode) AS tray_code_count
    FROM 
        SubqueryCTE
    GROUP BY 
        scan_date,
        batchno,
        production_date
),
FilteredAggregateCTE AS (
    SELECT 
        scan_date,
        batchno,
        production_date,
        MIN(scan_time) AS min_scan_time,
        MAX(scan_time) AS max_scan_time,
        COUNT(*) AS total_scan_count,
        SUM(CASE WHEN RIGHT(productid, 2) <> 'NG' THEN 1 ELSE 0 END) AS scan_success_count,
        SUM(CASE WHEN RIGHT(productid, 2) = 'NG' THEN 1 ELSE 0 END) AS scan_failure_count,
        COUNT(DISTINCT traycode) AS tray_code_count
    FROM 
        FilteredCTE
    GROUP BY 
        scan_date,
        batchno,
        production_date
)
SELECT 
    -- 原有完整统计数据
    CONVERT(VARCHAR(10), a.scan_date, 23) + '|' + a.batchno + '|' + a.production_date + '|' + 
    RIGHT('000000' + CAST(ROW_NUMBER() OVER (ORDER BY a.scan_date, a.batchno, a.production_date) AS VARCHAR(6)), 6) AS id,
    a.scan_date,
    CONVERT(VARCHAR(10), a.min_scan_time, 108) AS 'scan_time_start',
    CONVERT(VARCHAR(10), a.max_scan_time, 108) AS 'scan_time_end',
    a.batchno AS 'batch_number',
    a.production_date,
    a.total_scan_count,
    CAST(a.scan_success_count AS VARCHAR(10)) AS 'scan_success_count',
    CAST(a.scan_failure_count AS VARCHAR(10)) AS 'scan_failure_count',
    CONVERT(VARCHAR(10), CAST(ROUND(a.scan_success_count * 1.0 / NULLIF(a.total_scan_count, 0) * 100, 2) AS decimal(5,2)), 2) AS 'success_rate',
    CONVERT(VARCHAR(10), CAST(ROUND(a.scan_failure_count * 1.0 / NULLIF(a.total_scan_count, 0) * 100, 2) AS decimal(5,2)), 2) AS 'error_code_rate',
    DATEDIFF(MINUTE, a.min_scan_time, a.max_scan_time) AS 'total_running_time_minutes',
    CONVERT(VARCHAR(10), CAST(ROUND(a.total_scan_count * 1.0 / NULLIF(DATEDIFF(MINUTE, a.min_scan_time, a.max_scan_time), 0), 2) AS decimal(10,2)), 2) AS 'total_daily_efficiency_per_minute',
    CONVERT(VARCHAR(10), CAST(ROUND(DATEDIFF(MINUTE, a.min_scan_time, a.max_scan_time) * 1.0 / NULLIF(a.tray_code_count, 0), 2) AS decimal(10,2)), 2) AS 'total_daily_efficiency_per_tray_code_count',
    a.tray_code_count AS 'total_tray_code_count',
    
    -- 剔除最后托盘后的统计数据
    -- 去除后的运行时间
    DATEDIFF(MINUTE, f.min_scan_time, f.max_scan_time) AS 'filtered_running_time_minutes', 
    -- 去除后的托盘数量
    f.tray_code_count AS 'filtered_tray_code_count', 
    --去除后的运行效率
    CONVERT(VARCHAR(10), CAST(ROUND(DATEDIFF(MINUTE, f.min_scan_time, f.max_scan_time) * 1.0 / NULLIF(f.tray_code_count, 0), 2) AS decimal(10,2)), 2) AS 'filtered_daily_efficiency_per_tray_code_count', 
    
    -- 差异统计
    a.tray_code_count - f.tray_code_count AS 'removed_tray_count',
    DATEDIFF(MINUTE, a.min_scan_time, a.max_scan_time) - DATEDIFF(MINUTE, f.min_scan_time, f.max_scan_time) AS 'reduced_running_time_minutes'
FROM 
    AggregateCTE a
    LEFT JOIN FilteredAggregateCTE f ON 
        a.scan_date = f.scan_date AND 
        a.batchno = f.batchno AND 
        a.production_date = f.production_date
        WHERE f.tray_code_count > 0;
GO



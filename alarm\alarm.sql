CREATE VIEW v_alarm_event_statistics AS
-- 步骤1：计算相邻记录的时间差
WITH TimeDiffCTE AS (
    SELECT 
        id,
        alarm_id,
        alarm_name,
        alarm_info,
        db_address,
        alarm_type,
        alarm_time,
        -- 计算与下一条记录的时间差(秒)
        DATEDIFF(SECOND, alarm_time, LEAD(alarm_time) OVER (PARTITION BY db_address ORDER BY alarm_time)) AS next_time_diff,
        -- 计算与上一条记录的时间差(秒)
        DATEDIFF(SECOND, LAG(alarm_time) OVER (PARTITION BY db_address ORDER BY alarm_time), alarm_time) AS prev_time_diff
    FROM 
        equipment_alarm_info
),
-- 步骤2：标记新组的起点
GroupStartCTE AS (
    SELECT 
        *,
        CASE 
            WHEN prev_time_diff IS NULL OR prev_time_diff > 30 THEN 1 -- 第一条记录或间隔>30秒的是新组起点
            ELSE 0 
        END AS is_new_group
    FROM 
        TimeDiffCTE
),
-- 步骤3：计算累计组号
GroupedCTE AS (
    SELECT 
        *,
        SUM(is_new_group) OVER (PARTITION BY db_address ORDER BY alarm_time ROWS UNBOUNDED PRECEDING) AS group_id
    FROM 
        GroupStartCTE
)
-- 步骤4：汇总每个组的信息
SELECT 
		alarm_id,
    db_address,
    alarm_info,
    MIN(alarm_time) AS start_time,
    MAX(alarm_time) AS end_time,
    DATEDIFF(SECOND, MIN(alarm_time), MAX(alarm_time)) AS duration_seconds,
    COUNT(*) AS alarm_count,
    -- 格式化持续时间
    CONCAT(
        FLOOR(DATEDIFF(SECOND, MIN(alarm_time), MAX(alarm_time)) / 60), '分',
        DATEDIFF(SECOND, MIN(alarm_time), MAX(alarm_time)) % 60, '秒'
    ) AS duration_formatted
FROM 
    GroupedCTE
GROUP BY 
    alarm_id,
    db_address,
    group_id,
		alarm_info;